# =============================================================================
# Docker ignore file for Face Recognition Service
# =============================================================================

# Git
.git
.gitignore
.gitattributes

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Environment files (will be provided at runtime)
.env*
!.env.atlas

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
*.tmp

# Test files and uploads
test_images/
uploads/
temp_uploads/

# Documentation
README.md
docs/
*.md

# Deployment scripts
deploy.sh
scripts/

# Database dumps
*.dump
*.sql

# Backup files
*.bak
*.backup

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
