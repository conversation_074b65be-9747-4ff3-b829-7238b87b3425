from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
from contextlib import asynccontextmanager
from config import config
# import sentry_sdk  # Optional monitoring service
from routes import register, validate
from services.mongo_service import mongo_service
from services.cache_service import cache_service
from services.monitoring_service import monitoring_service
from middleware.performance_middleware import PerformanceMiddleware, CacheMetricsMiddleware
from models import HealthResponse, ErrorResponse
import sentry_sdk

sentry_sdk.init(
    dsn="https://<EMAIL>/4509634698412112",
    send_default_pii=True,
    traces_sample_rate=1.0,

)

logging.basicConfig(
    level=config.get_log_level(),
    format=config.LOG_FORMAT
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting Face Recognition Service...")

    # Initialize MongoDB connection
    if mongo_service.connect():
        logger.info("MongoDB connection established successfully")
    else:
        logger.error("Failed to connect to MongoDB")

    # Initialize cache service
    if config.CACHE_ENABLED:
        if await cache_service.connect():
            logger.info("Cache service connected successfully")

            # Warm cache on startup if enabled
            if config.CACHE_WARM_ON_STARTUP:
                try:
                    embeddings = mongo_service.find_all_embeddings()
                    if embeddings:
                        await cache_service.warm_cache(embeddings)
                        logger.info(f"Cache warmed with {len(embeddings)} embeddings")
                    else:
                        logger.info("No embeddings found to warm cache")
                except Exception as e:
                    logger.warning(f"Cache warming failed: {e}")
        else:
            logger.warning("Cache service connection failed, running without cache")
    else:
        logger.info("Cache service disabled")

    # Start monitoring service
    await monitoring_service.start_monitoring()
    logger.info("Performance monitoring started")

    yield

    logger.info("Shutting down Face Recognition Service...")

    # Cleanup services
    await monitoring_service.stop_monitoring()

    if config.CACHE_ENABLED:
        await cache_service.disconnect()

    mongo_service.disconnect()

    # Cleanup similarity calculator thread pool
    from utils.similarity import similarity_calculator
    similarity_calculator.cleanup()




app = FastAPI(
    title="Face Recognition Service",
    description="""
    A comprehensive Face Recognition Service API built with FastAPI and MongoDB.

    ## Features

    * **Face Registration**: Register multiple face images for a person with work unit/agency info
    * **Face Validation**: Simplified validation against ALL registered faces (no filtering required)
    * **MongoDB Storage**: Efficient storage of 128-dimensional face vectors
    * **Global Search**: Search across all embeddings regardless of work unit or agency
    * **Confidence Scoring**: Returns both similarity score and confidence percentage

    ## Technology Stack

    * FastAPI for the web framework
    * MongoDB for face vector storage
    * face_recognition library for face detection and encoding
    * 128-dimensional face vectors for high accuracy

    ## Usage

    1. **Register faces**: Upload minimum 5 images per person using `/register-face`
    2. **Validate faces**: Upload a single image for validation using `/validate-face` (searches all records)
    3. **Monitor system**: Check health and statistics using `/health` and `/validation-stats`

    ## API Changes (v2.0)

    * **Simplified Validation**: No need to provide work_unit_id or agency_id for validation
    * **Global Search**: Validation searches across all registered faces
    * **Enhanced Response**: Returns complete user info including work_unit_id and agency_id
    * **Confidence Score**: Added confidence percentage alongside similarity score
    """,
    version="2.0.0",
    contact={
        "name": "Face Recognition Service",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan
)

# Add performance monitoring middleware
app.add_middleware(PerformanceMiddleware)
app.add_middleware(CacheMetricsMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=config.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(
    register.router,
    tags=["Face Registration"],
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)

app.include_router(
    validate.router,
    tags=["Face Validation"],
    responses={
        400: {"model": ErrorResponse},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)


@app.get(
    "/",
    summary="Root endpoint",
    description="Welcome message and basic API information"
)
async def root():
    return {
        "message": "Welcome to Face Recognition Service API",
        "version": "1.0.0",
        "documentation": "/docs",
        "health_check": "/health",
        "endpoints": {
            "register_face": "/register-face",
            "validate_face": "/validate-face",
            "validation_stats": "/validation-stats"
        }
    }


@app.get(
    "/health",
    response_model=HealthResponse,
    summary="Health check endpoint",
    description="Check the health status of the service and database connection"
)
async def health_check():
    """Healh check endpoint"""
    try:
        db_connected = mongo_service.is_connected()
        
        if not db_connected:
            db_connected = mongo_service.connect()
        
        status = "healthy" if db_connected else "unhealthy"
        message = "Service is running normally" if db_connected else "Database connection failed"
        
        response = HealthResponse(
            status=status,
            message=message,
            database_connected=db_connected
        )
        
        if db_connected:
            return response
        else:
            return JSONResponse(
                status_code=503,
                content=response.dict()
            )
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content=HealthResponse(
                status="unhealthy",
                message=f"Health check failed: {str(e)}",
                database_connected=False
            ).dict()
        )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            detail=f"HTTP {exc.status_code} error occurred"
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail="An unexpected error occurred. Please try again later."
        ).dict()
    )

@app.get(
    "/cache-stats",
    summary="Get cache performance statistics",
    description="Returns cache hit rates, performance metrics, and configuration details"
)
async def get_cache_stats():
    """Get cache performance statistics"""
    try:
        if not config.CACHE_ENABLED:
            return {
                "cache_enabled": False,
                "message": "Cache is disabled"
            }

        stats = await cache_service.get_cache_stats()
        return stats

    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Failed to retrieve cache statistics",
                "detail": str(e)
            }
        )

@app.post(
    "/cache-refresh",
    summary="Manually refresh cache",
    description="Force refresh the face embeddings cache"
)
async def refresh_cache():
    """Manually refresh the cache"""
    try:
        if not config.CACHE_ENABLED:
            return {
                "cache_enabled": False,
                "message": "Cache is disabled"
            }

        # Get fresh data from database
        embeddings = mongo_service.find_all_embeddings()

        if not embeddings:
            return {
                "success": False,
                "message": "No embeddings found to cache"
            }

        # Refresh cache
        success = await cache_service.cache_face_embeddings(embeddings)

        if success:
            return {
                "success": True,
                "message": f"Cache refreshed with {len(embeddings)} embeddings"
            }
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "message": "Failed to refresh cache"
                }
            )

    except Exception as e:
        logger.error(f"Error refreshing cache: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Failed to refresh cache",
                "detail": str(e)
            }
        )

@app.get(
    "/performance-metrics",
    summary="Get comprehensive performance metrics",
    description="Returns detailed performance statistics including response times, system metrics, and face recognition stats"
)
async def get_performance_metrics():
    """Get comprehensive performance metrics"""
    try:
        metrics = await monitoring_service.get_performance_summary()
        return metrics

    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Failed to retrieve performance metrics",
                "detail": str(e)
            }
        )

@app.get(
    "/health-detailed",
    summary="Detailed health check with metrics",
    description="Returns comprehensive health status with system and service metrics"
)
async def detailed_health_check():
    """Detailed health check with performance metrics"""
    try:
        health_metrics = await monitoring_service.get_health_metrics()

        # Add database and cache status
        health_metrics["services"] = {
            "database": mongo_service.is_connected(),
            "cache": await cache_service.is_cache_available() if config.CACHE_ENABLED else False
        }

        status_code = 200
        if health_metrics["status"] == "degraded":
            status_code = 503
        elif health_metrics["status"] == "unknown":
            status_code = 500

        return JSONResponse(
            status_code=status_code,
            content=health_metrics
        )

    except Exception as e:
        logger.error(f"Error in detailed health check: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(e)
            }
        )

@app.get("/sentry-debug")
async def trigger_error():
    print("MASUK")
    division_by_zero = 1 / 0

if __name__ == "__main__":
    import uvicorn

    if config.is_development():
        config.print_config()

    logger.info(f"Starting server on {config.HOST}:{config.PORT}")

    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.RELOAD,
        log_level=config.LOG_LEVEL.lower()
    )
