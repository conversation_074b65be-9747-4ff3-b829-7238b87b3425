#!/usr/bin/env python3
"""
Load testing script for Face Recognition Service
Tests concurrent user handling up to 1000 users
"""
import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict, Any
from pathlib import Path
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoadTester:
    """Load testing class for Face Recognition Service"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None
        self.results: List[Dict[str, Any]] = []
        
    async def __aenter__(self):
        """Async context manager entry"""
        connector = aiohttp.TCPConnector(
            limit=1000,  # Total connection pool size
            limit_per_host=100,  # Per-host connection limit
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=120, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> bool:
        """Check if service is healthy"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def test_validate_face_endpoint(self, user_id: int) -> Dict[str, Any]:
        """Test the validate-face endpoint with a sample image"""
        start_time = time.time()
        
        try:
            # Create a dummy image file for testing
            # In real testing, you would use actual face images
            dummy_image_data = b"dummy_image_content_for_testing"
            
            data = aiohttp.FormData()
            data.add_field('image', dummy_image_data, filename='test_face.jpg', content_type='image/jpeg')
            
            async with self.session.post(
                f"{self.base_url}/validate-face",
                data=data
            ) as response:
                response_time = time.time() - start_time
                status_code = response.status
                
                try:
                    response_data = await response.json()
                except:
                    response_data = {"error": "Could not parse response"}
                
                return {
                    "user_id": user_id,
                    "endpoint": "validate-face",
                    "status_code": status_code,
                    "response_time": response_time,
                    "success": 200 <= status_code < 300,
                    "response_data": response_data
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "user_id": user_id,
                "endpoint": "validate-face",
                "status_code": 0,
                "response_time": response_time,
                "success": False,
                "error": str(e)
            }
    
    async def test_cache_stats_endpoint(self, user_id: int) -> Dict[str, Any]:
        """Test the cache-stats endpoint"""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/cache-stats") as response:
                response_time = time.time() - start_time
                status_code = response.status
                
                try:
                    response_data = await response.json()
                except:
                    response_data = {"error": "Could not parse response"}
                
                return {
                    "user_id": user_id,
                    "endpoint": "cache-stats",
                    "status_code": status_code,
                    "response_time": response_time,
                    "success": 200 <= status_code < 300,
                    "response_data": response_data
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "user_id": user_id,
                "endpoint": "cache-stats",
                "status_code": 0,
                "response_time": response_time,
                "success": False,
                "error": str(e)
            }
    
    async def test_performance_metrics_endpoint(self, user_id: int) -> Dict[str, Any]:
        """Test the performance-metrics endpoint"""
        start_time = time.time()
        
        try:
            async with self.session.get(f"{self.base_url}/performance-metrics") as response:
                response_time = time.time() - start_time
                status_code = response.status
                
                try:
                    response_data = await response.json()
                except:
                    response_data = {"error": "Could not parse response"}
                
                return {
                    "user_id": user_id,
                    "endpoint": "performance-metrics",
                    "status_code": status_code,
                    "response_time": response_time,
                    "success": 200 <= status_code < 300,
                    "response_data": response_data
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "user_id": user_id,
                "endpoint": "performance-metrics",
                "status_code": 0,
                "response_time": response_time,
                "success": False,
                "error": str(e)
            }
    
    async def run_concurrent_test(self, num_users: int, test_duration: int = 60) -> Dict[str, Any]:
        """Run concurrent load test"""
        logger.info(f"Starting load test with {num_users} concurrent users for {test_duration} seconds")
        
        # Check service health first
        if not await self.health_check():
            raise Exception("Service health check failed")
        
        start_time = time.time()
        end_time = start_time + test_duration
        
        tasks = []
        user_id = 0
        
        while time.time() < end_time:
            # Create batch of concurrent requests
            batch_size = min(num_users, 50)  # Limit batch size to avoid overwhelming
            
            for _ in range(batch_size):
                user_id += 1
                
                # Mix different endpoints for realistic testing
                if user_id % 10 == 0:
                    task = self.test_performance_metrics_endpoint(user_id)
                elif user_id % 5 == 0:
                    task = self.test_cache_stats_endpoint(user_id)
                else:
                    task = self.test_validate_face_endpoint(user_id)
                
                tasks.append(task)
            
            # Execute batch and collect results
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, dict):
                    self.results.append(result)
                else:
                    logger.error(f"Task failed with exception: {result}")
            
            tasks.clear()
            
            # Small delay between batches
            await asyncio.sleep(0.1)
        
        total_time = time.time() - start_time
        
        # Calculate statistics
        successful_requests = [r for r in self.results if r.get('success', False)]
        failed_requests = [r for r in self.results if not r.get('success', False)]
        
        response_times = [r['response_time'] for r in successful_requests]
        
        stats = {
            "test_duration": total_time,
            "total_requests": len(self.results),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": (len(successful_requests) / len(self.results)) * 100 if self.results else 0,
            "requests_per_second": len(self.results) / total_time if total_time > 0 else 0,
            "response_times": {
                "min": min(response_times) if response_times else 0,
                "max": max(response_times) if response_times else 0,
                "mean": statistics.mean(response_times) if response_times else 0,
                "median": statistics.median(response_times) if response_times else 0,
                "p95": statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else 0,
                "p99": statistics.quantiles(response_times, n=100)[98] if len(response_times) > 100 else 0,
            },
            "endpoint_breakdown": {}
        }
        
        # Breakdown by endpoint
        endpoints = set(r.get('endpoint', 'unknown') for r in self.results)
        for endpoint in endpoints:
            endpoint_results = [r for r in self.results if r.get('endpoint') == endpoint]
            endpoint_successful = [r for r in endpoint_results if r.get('success', False)]
            
            stats["endpoint_breakdown"][endpoint] = {
                "total": len(endpoint_results),
                "successful": len(endpoint_successful),
                "success_rate": (len(endpoint_successful) / len(endpoint_results)) * 100 if endpoint_results else 0
            }
        
        return stats


async def main():
    """Main load testing function"""
    parser = argparse.ArgumentParser(description="Load test Face Recognition Service")
    parser.add_argument("--url", default="http://localhost:8000", help="Service URL")
    parser.add_argument("--users", type=int, default=100, help="Number of concurrent users")
    parser.add_argument("--duration", type=int, default=60, help="Test duration in seconds")
    parser.add_argument("--output", help="Output file for results (JSON)")
    
    args = parser.parse_args()
    
    async with LoadTester(args.url) as tester:
        try:
            results = await tester.run_concurrent_test(args.users, args.duration)
            
            print("\n" + "="*60)
            print("LOAD TEST RESULTS")
            print("="*60)
            print(f"Test Duration: {results['test_duration']:.2f} seconds")
            print(f"Total Requests: {results['total_requests']}")
            print(f"Successful Requests: {results['successful_requests']}")
            print(f"Failed Requests: {results['failed_requests']}")
            print(f"Success Rate: {results['success_rate']:.2f}%")
            print(f"Requests/Second: {results['requests_per_second']:.2f}")
            print()
            print("Response Times:")
            print(f"  Min: {results['response_times']['min']:.4f}s")
            print(f"  Max: {results['response_times']['max']:.4f}s")
            print(f"  Mean: {results['response_times']['mean']:.4f}s")
            print(f"  Median: {results['response_times']['median']:.4f}s")
            print(f"  95th percentile: {results['response_times']['p95']:.4f}s")
            print(f"  99th percentile: {results['response_times']['p99']:.4f}s")
            print()
            print("Endpoint Breakdown:")
            for endpoint, stats in results['endpoint_breakdown'].items():
                print(f"  {endpoint}: {stats['successful']}/{stats['total']} ({stats['success_rate']:.1f}%)")
            
            if args.output:
                with open(args.output, 'w') as f:
                    json.dump(results, f, indent=2)
                print(f"\nDetailed results saved to: {args.output}")
            
        except Exception as e:
            logger.error(f"Load test failed: {e}")
            return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
