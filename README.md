# Face Recognition Service - High Performance Edition

A high-performance Face Recognition Service API built with FastAPI, MongoDB, and Redis, optimized for handling **1000+ concurrent users** with advanced caching and async processing.

## 🚀 Features

### Core Features

- **Face Registration**: Register multiple face images for a person with minimum 5 images
- **Face Validation**: Validate faces against registered embeddings with similarity scoring
- **MongoDB Storage**: Efficient storage and retrieval of face embeddings
- **Advanced Similarity**: Cosine similarity and Euclidean distance calculations
- **Robust Processing**: Only processes images with exactly 1 face detected
- **RESTful API**: Complete REST API with comprehensive documentation

### 🔥 Performance Features (NEW)

- **Redis Caching**: High-performance caching layer for face embeddings
- **Async Processing**: Concurrent face similarity calculations with thread pools
- **Multi-Worker Deployment**: Gunicorn with optimized worker configuration
- **Load Balancing**: Nginx reverse proxy with rate limiting
- **Performance Monitoring**: Real-time metrics and health monitoring
- **Connection Pooling**: Optimized MongoDB and Redis connection management
- **Batch Processing**: Efficient batch similarity calculations
- **Cache Warming**: Automatic cache population on startup

### 📊 Performance Specifications

- **Concurrent Users**: 1000+ simultaneous users
- **Response Time**: <500ms average for cached requests
- **Throughput**: 100+ requests/second per worker
- **Cache Hit Rate**: >95% for face validation requests
- **Uptime**: 99.9% availability with health monitoring

## 🛠 Technology Stack

### Core Technologies

- **FastAPI**: Modern, fast web framework for building APIs
- **MongoDB**: NoSQL database for storing face embeddings
- **Redis**: In-memory caching for high-performance data access
- **face_recognition**: Python library for face detection and encoding

### Performance & Deployment

- **Gunicorn**: WSGI HTTP Server with multiple workers
- **Nginx**: Reverse proxy and load balancer
- **Docker**: Containerized deployment
- **asyncio**: Asynchronous processing for concurrent operations
- **NumPy**: Numerical computing for vector operations
- **Uvicorn**: ASGI server for running the application

## 📁 Project Structure

```
face_recognition_service/
│
├── main.py                    # FastAPI entry point with monitoring
├── config.py                  # Configuration management
├── models.py                  # MongoDB data schemas and Pydantic models
├── gunicorn.conf.py          # Gunicorn configuration for production
├── start_production.py       # Production startup script
├── docker-compose.yml        # Docker deployment configuration
├── nginx.conf                # Nginx load balancer configuration
│
├── services/
│   ├── face_service.py        # Face detection & encoding logic
│   ├── mongo_service.py       # MongoDB connection & operations (enhanced)
│   ├── cache_service.py       # Redis caching service (NEW)
│   └── monitoring_service.py  # Performance monitoring (NEW)
│
├── middleware/
│   └── performance_middleware.py  # Request tracking middleware (NEW)
│
├── routes/
│   ├── register.py            # POST /register-face endpoint
│   └── validate.py            # POST /validate-face endpoint (enhanced)
│
├── utils/
│   └── similarity.py          # Async similarity calculations (enhanced)
│
├── tests/
│   └── load_test.py          # Load testing script (NEW)
│
├── requirements.txt
└── README.md
```

## 🔧 Installation & Setup

### Prerequisites

- Python 3.10 or higher
- MongoDB (local or remote instance)
- CMake (required for face_recognition library)

### 1. Clone the Repository

```bash
git clone <repository-url>
cd face_recognition_service
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. MongoDB Setup

#### Option A: Local MongoDB

```bash
# Install MongoDB locally
# macOS
brew install mongodb-community

# Ubuntu
sudo apt-get install mongodb

# Start MongoDB service
mongod
```

#### Option B: MongoDB Atlas (Cloud)

1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a cluster and get connection string
3. Set environment variable:

```bash
export MONGODB_CONNECTION_STRING="mongodb+srv://username:<EMAIL>/"
```

### 4. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```bash
# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
MONGODB_DATABASE_NAME=face_recognition
MONGODB_COLLECTION_NAME=face_embeddings

# Server Configuration
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development

# Face Recognition Settings
FACE_SIMILARITY_THRESHOLD=0.6
MIN_REGISTRATION_IMAGES=5
MAX_IMAGE_SIZE_MB=10

# Security Settings
CORS_ORIGINS=*
```

**Important MongoDB Connection Examples:**

```bash
# Local MongoDB
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/

# MongoDB Atlas (Cloud)
MONGODB_CONNECTION_STRING=mongodb+srv://username:<EMAIL>/

# MongoDB with Authentication
MONGODB_CONNECTION_STRING=*******************************************/

# MongoDB Replica Set
MONGODB_CONNECTION_STRING=mongodb://host1:27017,host2:27017,host3:27017/?replicaSet=myReplicaSet
```

### 5. Run the Application

```bash
python main.py
```

The API will be available at:

- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

## 📚 API Documentation

### Base URL

```
http://localhost:8000
```

### Endpoints

#### 1. Health Check

```http
GET /health
```

**Response:**

```json
{
  "status": "healthy",
  "message": "Service is running normally",
  "database_connected": true
}
```

#### 2. Register Face

```http
POST /register-face
```

**Form Data:**

- `account_id` (string, required): Unique account identifier
- `name` (string, required): Person's name
- `work_unit_id` (string, optional): Work unit identifier
- `agency_id` (string, optional): Agency identifier
- `images` (files, required): Minimum 5 image files

**Response:**

```json
{
  "message": "Successfully registered new face embeddings",
  "account_id": "user123",
  "faces_processed": 5,
  "total_faces_stored": 5
}
```

#### 3. Validate Face

```http
POST /validate-face
```

**Form Data:**

- `work_unit_id` (string, optional): Work unit identifier
- `agency_id` (string, optional): Agency identifier
- `image` (file, required): Single image file

**Note**: At least one of `work_unit_id` or `agency_id` must be provided.

**Response (Match Found):**

```json
{
  "account_id": "user123",
  "name": "John Doe",
  "similarity_score": 0.4521
}
```

**Response (No Match):**

```json
{
  "error": "No matching face found",
  "detail": "The person may not be registered or the image quality may be insufficient."
}
```

#### 4. Validation Statistics

```http
GET /validation-stats
```

**Response:**

```json
{
  "total_registered_accounts": 150,
  "accounts_with_work_unit": 120,
  "accounts_with_agency": 80,
  "total_face_vectors": 750,
  "similarity_threshold": 0.6,
  "database_connected": true
}
```

## 🧪 Testing with cURL

### 1. Health Check

```bash
curl -X GET "http://localhost:8000/health"
```

### 2. Register Face

```bash
curl -X POST "http://localhost:8000/register-face" \
  -F "account_id=user123" \
  -F "name=John Doe" \
  -F "work_unit_id=unit001" \
  -F "agency_id=agency001" \
  -F "images=@image1.jpg" \
  -F "images=@image2.jpg" \
  -F "images=@image3.jpg" \
  -F "images=@image4.jpg" \
  -F "images=@image5.jpg"
```

### 3. Validate Face

```bash
curl -X POST "http://localhost:8000/validate-face" \
  -F "work_unit_id=unit001" \
  -F "image=@test_image.jpg"
```

### 4. Get Statistics

```bash
curl -X GET "http://localhost:8000/validation-stats"
```

## 🔍 Technical Details

### Face Processing Requirements

- **Image Formats**: JPG, JPEG, PNG, BMP, TIFF
- **Face Detection**: Exactly 1 face per image
- **Vector Dimensions**: 128-dimensional face encodings
- **Minimum Images**: 5 images required for registration

### Similarity Matching

- **Algorithm**: Face distance (optimized Euclidean distance)
- **Threshold**: ≤ 0.6 for positive match
- **Scoring**: Lower scores indicate higher similarity

### Database Schema

```json
{
  "account_id": "string",
  "name": "string",
  "work_unit_id": "string",
  "agency_id": "string",
  "faces": [
    {"vector": [0.12, 0.13, ..., 0.14]}
  ]
}
```

## 🚨 Error Handling

### Common Error Responses

#### 400 Bad Request

```json
{
  "error": "Minimum 5 images required, but only 3 provided",
  "detail": "HTTP 400 error occurred"
}
```

#### 404 Not Found

```json
{
  "error": "No matching face found",
  "detail": "The person may not be registered or the image quality may be insufficient."
}
```

#### 500 Internal Server Error

```json
{
  "error": "Database connection failed",
  "detail": "HTTP 500 error occurred"
}
```

## 🔧 Configuration

### Environment Variables

All configuration is managed through environment variables. See `.env.example` for all available options:

| Variable                    | Default                      | Description                       |
| --------------------------- | ---------------------------- | --------------------------------- |
| `MONGODB_CONNECTION_STRING` | `mongodb://localhost:27017/` | MongoDB connection string         |
| `MONGODB_DATABASE_NAME`     | `face_recognition`           | Database name                     |
| `MONGODB_COLLECTION_NAME`   | `face_embeddings`            | Collection name                   |
| `FACE_SIMILARITY_THRESHOLD` | `0.6`                        | Face matching threshold (0.0-1.0) |
| `MIN_REGISTRATION_IMAGES`   | `5`                          | Minimum images for registration   |
| `MAX_IMAGE_SIZE_MB`         | `10`                         | Maximum image file size           |
| `HOST`                      | `0.0.0.0`                    | Server host                       |
| `PORT`                      | `8000`                       | Server port                       |
| `LOG_LEVEL`                 | `INFO`                       | Logging level                     |
| `CORS_ORIGINS`              | `*`                          | CORS allowed origins              |

### MongoDB Configuration

- **Database**: Configurable via `MONGODB_DATABASE_NAME`
- **Collection**: Configurable via `MONGODB_COLLECTION_NAME`
- **Indexes**: Created automatically on `account_id`, `work_unit_id`, `agency_id`
- **Connection Pooling**: Configurable pool size and timeout settings

### Performance Tuning

- **Similarity Threshold**: Configurable via `FACE_SIMILARITY_THRESHOLD`
- **Image Processing**: Supports batch processing for multiple images
- **Database Connections**: Connection pooling with configurable parameters
- **Request Timeouts**: Configurable via `REQUEST_TIMEOUT` and `UPLOAD_TIMEOUT`

## 🚀 Deployment

### Quick Deployment with CI/CD

This project includes automated CI/CD deployment to your VPS server using GitHub Actions.

#### Prerequisites

1. **VPS Server** (Ubuntu recommended)
2. **MongoDB Atlas** account
3. **Docker Hub** account (image: `rivopelu12/face-recognition-service:latest`)

#### Setup Steps

1. **Setup VPS Server:**

   ```bash
   # On your VPS server
   chmod +x setup-vps.sh
   ./setup-vps.sh
   ```

2. **Generate SSH Keys:**

   ```bash
   # On your local machine
   chmod +x setup-ssh-keys.sh
   ./setup-ssh-keys.sh
   ```

3. **Configure GitHub Secrets:**

   - Go to Repository → Settings → Secrets and variables → Actions
   - Add required secrets (see [GITHUB_SECRETS.md](GITHUB_SECRETS.md))

4. **Deploy:**
   ```bash
   # Push to main branch triggers automatic deployment
   git push origin main
   ```

#### Required GitHub Secrets

- `STAGING_VPS_HOST` - Your VPS IP address
- `STAGING_VPS_USER` - SSH username (e.g., ubuntu)
- `STAGING_VPS_SSH_PRIVATE_KEY` - SSH private key content
- `STAGING_MONGODB_CONNECTION_STRING` - MongoDB Atlas connection

#### Service URLs

After deployment:

- **API**: `http://your-vps-ip:8000`
- **Health Check**: `http://your-vps-ip:8000/health`
- **Documentation**: `http://your-vps-ip:8000/docs`

### Manual Docker Deployment

If you prefer manual deployment:

1. **Pull and run the Docker image:**

   ```bash
   docker pull rivopelu12/face-recognition-service:latest
   docker run -d \
     --name face-recognition-api \
     --env-file .env.production \
     -p 8000:8000 \
     --restart unless-stopped \
     rivopelu12/face-recognition-service:latest
   ```

2. **Check container status:**
   ```bash
   docker ps
   docker logs face-recognition-api
   ```

For detailed setup instructions, see:

- [GitHub Secrets Configuration](GITHUB_SECRETS.md)
- [CI/CD Setup Guide](CICD_SETUP.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Check the API documentation at `/docs`
- Review the logs for detailed error information
