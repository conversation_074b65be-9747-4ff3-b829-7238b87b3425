version: '3.8'

services:
  face-recognition-service:
    build: .
    ports:
      - "8000:8000"
    environment:
      # Server Configuration
      - HOST=0.0.0.0
      - PORT=8000
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      
      # MongoDB Configuration
      - MONGODB_CONNECTION_STRING=********************************************************************
      - MONGODB_DATABASE_NAME=prod_testing
      - MONGODB_COLLECTION_NAME=face_embeddings
      - MONGODB_MAX_POOL_SIZE=200
      - MONGODB_MIN_POOL_SIZE=20
      - MON<PERSON><PERSON><PERSON>_MAX_IDLE_TIME_MS=30000
      
      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - REDIS_MAX_CONNECTIONS=200
      - REDIS_SOCKET_TIMEOUT=5
      - REDIS_SOCKET_CONNECT_TIMEOUT=5
      
      # Cache Configuration
      - CACHE_ENABLED=true
      - CACHE_TTL_SECONDS=3600
      - CACHE_WARM_ON_STARTUP=true
      - CACHE_REFRESH_INTERVAL=1800
      
      # Performance Configuration
      - MAX_CONCURRENT_SIMILARITY_CALCULATIONS=100
      - SIMILARITY_BATCH_SIZE=50
      - ENABLE_ASYNC_PROCESSING=true
      - REQUEST_TIMEOUT=30
      - UPLOAD_TIMEOUT=60
      
      # Gunicorn Configuration
      - GUNICORN_WORKERS=8
      
      # Face Recognition Configuration
      - FACE_SIMILARITY_THRESHOLD=0.6
      - MIN_REGISTRATION_IMAGES=5
      - MAX_IMAGE_SIZE_MB=10
    
    depends_on:
      - redis
    
    volumes:
      - ./logs:/app/logs
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - face-recognition-service
    restart: unless-stopped
    
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

volumes:
  redis_data:
    driver: local

networks:
  default:
    driver: bridge
