#!/usr/bin/env python3
"""
Test script to verify the face validation endpoint is working properly
"""

import requests
import io
from PIL import Image
import numpy as np

BASE_URL = "http://localhost:8000"

def create_test_image():
    """Create a simple test image"""
    # Create a simple 200x200 RGB image
    img_array = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_face_validation():
    """Test the face validation endpoint"""
    print("🧪 Testing Face Validation Endpoint")
    print("=" * 50)
    
    try:
        # Create test image
        test_image = create_test_image()
        
        # Prepare the request
        files = {
            'image': ('test_face.jpg', test_image, 'image/jpeg')
        }
        
        # Make the request
        print("📤 Sending request to /validate-face...")
        response = requests.post(f"{BASE_URL}/validate-face", files=files, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {response.elapsed.total_seconds():.3f}s")
        
        if response.status_code == 200:
            print("✅ SUCCESS - Face validation endpoint is working!")
            result = response.json()
            print(f"📋 Response: {result}")
        elif response.status_code == 400:
            print("⚠️  Expected 400 - No face detected (this is normal for random image)")
            result = response.json()
            print(f"📋 Response: {result}")
        elif response.status_code == 500:
            print("❌ INTERNAL SERVER ERROR - This indicates the config issue")
            result = response.json()
            print(f"📋 Error: {result}")
            return False
        else:
            print(f"❓ Unexpected status code: {response.status_code}")
            print(f"📋 Response: {response.text}")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Face Validation Endpoint Test")
    print("=" * 60)
    
    success = test_face_validation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test completed successfully!")
        print("✅ The config import issue has been fixed")
        print("✅ Face validation endpoint is responding properly")
    else:
        print("❌ Test failed - there may still be issues")
    
    print("\n📋 Note:")
    print("   • A 400 response is expected for random images (no face detected)")
    print("   • A 500 response would indicate the config error is still present")
    print("   • A 200 response means face validation worked (unlikely with random image)")

if __name__ == "__main__":
    main()
