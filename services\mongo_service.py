import logging
import asyncio
from typing import List, Optional, Dict, Any
from pymongo import <PERSON>go<PERSON>lient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, DuplicateKeyError, ServerSelectionTimeoutError
from models import FaceEmbedding, FaceVector
from config import config
from services.cache_service import cache_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MongoService:
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
        self.connection_string = config.MONGODB_CONNECTION_STRING
        self.database_name = config.MONGODB_DATABASE_NAME
        self.collection_name = config.MONGODB_COLLECTION_NAME
        
    def connect(self) -> bool:
        try:
            # Enhanced connection configuration for high concurrency
            self.client = MongoClient(
                self.connection_string,
                maxPoolSize=config.MONGODB_MAX_POOL_SIZE,
                minPoolSize=config.MONGODB_MIN_POOL_SIZE,
                maxIdleTimeMS=config.MONGODB_MAX_IDLE_TIME_MS,
                # Additional settings for high concurrency
                serverSelectionTimeoutMS=5000,  # 5 seconds timeout
                connectTimeoutMS=10000,  # 10 seconds connection timeout
                socketTimeoutMS=30000,   # 30 seconds socket timeout
                waitQueueTimeoutMS=5000, # 5 seconds wait for connection from pool
                retryWrites=True,        # Enable retryable writes
                retryReads=True,         # Enable retryable reads
                maxConnecting=10,        # Max concurrent connections being established
                # Connection pool monitoring
                appname="FaceRecognitionService"
            )

            # Test connection with timeout
            self.client.admin.command('ping')
            self.database = self.client[self.database_name]
            self.collection = self.database[self.collection_name]

            # Create indexes for better query performance
            self.collection.create_index("account_id")
            self.collection.create_index("work_unit_id")
            self.collection.create_index("agency_id")
            # Compound index for better performance on common queries
            self.collection.create_index([("account_id", 1), ("work_unit_id", 1)])

            logger.info(f"Successfully connected to MongoDB with pool size {config.MONGODB_MAX_POOL_SIZE}")
            return True

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            return False
    
    def disconnect(self):
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    def is_connected(self) -> bool:
        try:
            if self.client:
                self.client.admin.command('ping')
                return True
        except:
            pass
        return False
    
    def insert_face_embedding(self, face_embedding: FaceEmbedding) -> bool:
        try:
            document = face_embedding.dict(by_alias=True, exclude={"id"})
            result = self.collection.insert_one(document)
            logger.info(f"Inserted face embedding with ID: {result.inserted_id}")

            # Invalidate cache after successful insert
            if config.CACHE_ENABLED:
                asyncio.create_task(cache_service.invalidate_cache())
                logger.debug("Cache invalidated after new embedding insert")

            return True

        except Exception as e:
            logger.error(f"Error inserting face embedding: {e}")
            return False
    
    def find_by_account_id(self, account_id: str) -> Optional[FaceEmbedding]:
        try:
            document = self.collection.find_one({"account_id": account_id})
            if document:
                return FaceEmbedding(**document)
            return None
            
        except Exception as e:
            logger.error(f"Error finding face embedding by account_id: {e}")
            return None
    
    def update_face_vectors(self, account_id: str, new_vectors: List[FaceVector]) -> bool:
        try:
            vector_dicts = [vector.dict() for vector in new_vectors]
            
            result = self.collection.update_one(
                {"account_id": account_id},
                {"$push": {"faces": {"$each": vector_dicts}}}
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated face vectors for account_id: {account_id}")
                return True
            else:
                logger.warning(f"No document updated for account_id: {account_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating face vectors: {e}")
            return False
    
    def find_by_work_unit_or_agency(self, work_unit_id: Optional[str] = None,
                                   agency_id: Optional[str] = None) -> List[FaceEmbedding]:
        try:
            query = {}
            if work_unit_id:
                query["work_unit_id"] = work_unit_id
            if agency_id:
                query["agency_id"] = agency_id

            if work_unit_id and agency_id:
                query = {"$or": [
                    {"work_unit_id": work_unit_id},
                    {"agency_id": agency_id}
                ]}

            documents = list(self.collection.find(query))
            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error finding face embeddings by work_unit/agency: {e}")
            return []

    def find_all_embeddings(self) -> List[FaceEmbedding]:
        try:
            documents = list(self.collection.find({}))
            logger.info(f"Retrieved {len(documents)} face embeddings from database")
            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error finding all face embeddings: {e}")
            return []

    async def find_all_embeddings_cached(self) -> List[FaceEmbedding]:
        """
        Get all face embeddings with caching support for high performance
        Falls back to database if cache is unavailable
        """
        try:
            # Try to get from cache first
            if config.CACHE_ENABLED:
                cached_embeddings = await cache_service.get_cached_face_embeddings()
                if cached_embeddings is not None:
                    logger.debug(f"Retrieved {len(cached_embeddings)} embeddings from cache")
                    return cached_embeddings

            # Cache miss or cache disabled - get from database
            logger.debug("Cache miss or disabled, fetching from database")
            embeddings = self.find_all_embeddings()

            # Cache the results for future requests
            if config.CACHE_ENABLED and embeddings:
                asyncio.create_task(cache_service.cache_face_embeddings(embeddings))

            return embeddings

        except Exception as e:
            logger.error(f"Error in cached embeddings retrieval: {e}")
            # Fallback to direct database query
            return self.find_all_embeddings()
    
    def get_total_faces_count(self, account_id: str) -> int:
        try:
            document = self.collection.find_one(
                {"account_id": account_id}, 
                {"faces": 1}
            )
            if document and "faces" in document:
                return len(document["faces"])
            return 0
            
        except Exception as e:
            logger.error(f"Error getting faces count: {e}")
            return 0
    
    def delete_by_account_id(self, account_id: str) -> bool:
        try:
            result = self.collection.delete_one({"account_id": account_id})
            if result.deleted_count > 0:
                logger.info(f"Deleted face embedding for account_id: {account_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting face embedding: {e}")
            return False


mongo_service = MongoService()
