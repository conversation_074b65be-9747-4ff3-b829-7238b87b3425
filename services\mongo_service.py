import logging
from typing import List, Optional, Dict, Any
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, DuplicateKeyError
from models import FaceEmbedding, FaceVector
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MongoService:
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
        self.connection_string = config.MONGODB_CONNECTION_STRING
        self.database_name = config.MONGODB_DATABASE_NAME
        self.collection_name = config.MONGODB_COLLECTION_NAME
        
    def connect(self) -> bool:
        try:
            self.client = MongoClient(
                self.connection_string,
                maxPoolSize=config.MONGODB_MAX_POOL_SIZE,
                minPoolSize=config.MONGODB_MIN_POOL_SIZE,
                maxIdleTimeMS=config.MONGODB_MAX_IDLE_TIME_MS
            )
            self.client.admin.command('ping')
            self.database = self.client[self.database_name]
            self.collection = self.database[self.collection_name]

            self.collection.create_index("account_id")
            self.collection.create_index("work_unit_id")
            self.collection.create_index("agency_id")

            logger.info("Successfully connected to MongoDB")
            return True
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            return False
    
    def disconnect(self):
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    def is_connected(self) -> bool:
        try:
            if self.client:
                self.client.admin.command('ping')
                return True
        except:
            pass
        return False
    
    def insert_face_embedding(self, face_embedding: FaceEmbedding) -> bool:
        try:
            document = face_embedding.dict(by_alias=True, exclude={"id"})
            result = self.collection.insert_one(document)
            logger.info(f"Inserted face embedding with ID: {result.inserted_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting face embedding: {e}")
            return False
    
    def find_by_account_id(self, account_id: str) -> Optional[FaceEmbedding]:
        try:
            document = self.collection.find_one({"account_id": account_id})
            if document:
                return FaceEmbedding(**document)
            return None
            
        except Exception as e:
            logger.error(f"Error finding face embedding by account_id: {e}")
            return None
    
    def update_face_vectors(self, account_id: str, new_vectors: List[FaceVector]) -> bool:
        try:
            vector_dicts = [vector.dict() for vector in new_vectors]
            
            result = self.collection.update_one(
                {"account_id": account_id},
                {"$push": {"faces": {"$each": vector_dicts}}}
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated face vectors for account_id: {account_id}")
                return True
            else:
                logger.warning(f"No document updated for account_id: {account_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating face vectors: {e}")
            return False
    
    def find_by_work_unit_or_agency(self, work_unit_id: Optional[str] = None,
                                   agency_id: Optional[str] = None) -> List[FaceEmbedding]:
        try:
            query = {}
            if work_unit_id:
                query["work_unit_id"] = work_unit_id
            if agency_id:
                query["agency_id"] = agency_id

            if work_unit_id and agency_id:
                query = {"$or": [
                    {"work_unit_id": work_unit_id},
                    {"agency_id": agency_id}
                ]}

            documents = list(self.collection.find(query))
            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error finding face embeddings by work_unit/agency: {e}")
            return []

    def find_all_embeddings(self) -> List[FaceEmbedding]:
        try:
            documents = list(self.collection.find({}))
            logger.info(f"Retrieved {len(documents)} face embeddings from database")
            return [FaceEmbedding(**doc) for doc in documents]

        except Exception as e:
            logger.error(f"Error finding all face embeddings: {e}")
            return []
    
    def get_total_faces_count(self, account_id: str) -> int:
        try:
            document = self.collection.find_one(
                {"account_id": account_id}, 
                {"faces": 1}
            )
            if document and "faces" in document:
                return len(document["faces"])
            return 0
            
        except Exception as e:
            logger.error(f"Error getting faces count: {e}")
            return 0
    
    def delete_by_account_id(self, account_id: str) -> bool:
        try:
            result = self.collection.delete_one({"account_id": account_id})
            if result.deleted_count > 0:
                logger.info(f"Deleted face embedding for account_id: {account_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting face embedding: {e}")
            return False


mongo_service = MongoService()
