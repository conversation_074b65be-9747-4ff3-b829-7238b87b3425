"""
Performance monitoring middleware for FastAPI
Automatically tracks request metrics and response times
"""
import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from services.monitoring_service import monitoring_service

logger = logging.getLogger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware to track request performance metrics"""
    
    def __init__(self, app, track_body_size: bool = True):
        super().__init__(app)
        self.track_body_size = track_body_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and track performance metrics"""
        start_time = time.time()
        
        # Extract endpoint information
        endpoint = f"{request.method} {request.url.path}"
        
        # Track face recognition specific metrics
        if request.url.path == "/validate-face":
            monitoring_service.increment_face_metric('validations_per_minute')
        elif request.url.path == "/register-face":
            monitoring_service.increment_face_metric('registrations_per_minute')
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Record metrics
            monitoring_service.record_request(
                endpoint=endpoint,
                response_time=response_time,
                status_code=response.status_code
            )
            
            # Add performance headers
            response.headers["X-Response-Time"] = f"{response_time:.4f}s"
            response.headers["X-Process-Time"] = f"{response_time * 1000:.2f}ms"
            
            return response
            
        except Exception as e:
            # Record error metrics
            response_time = time.time() - start_time
            monitoring_service.record_request(
                endpoint=endpoint,
                response_time=response_time,
                status_code=500
            )
            
            logger.error(f"Error processing request {endpoint}: {e}")
            raise


class CacheMetricsMiddleware(BaseHTTPMiddleware):
    """Middleware to track cache-related metrics"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Track cache operations"""
        
        # Track database operations for endpoints that use database
        if request.url.path in ["/validate-face", "/register-face", "/validation-stats"]:
            monitoring_service.increment_face_metric('database_operations')
        
        # Track cache operations (this will be incremented by cache service)
        if request.url.path in ["/validate-face", "/cache-stats", "/cache-refresh"]:
            monitoring_service.increment_face_metric('cache_operations')
        
        response = await call_next(request)
        
        # Add cache-related headers if available
        try:
            from services.cache_service import cache_service
            if await cache_service.is_cache_available():
                response.headers["X-Cache-Enabled"] = "true"
            else:
                response.headers["X-Cache-Enabled"] = "false"
        except Exception:
            response.headers["X-Cache-Enabled"] = "unknown"
        
        return response
