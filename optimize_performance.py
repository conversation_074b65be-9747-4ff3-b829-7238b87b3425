#!/usr/bin/env python3
"""
Performance optimization script for Face Recognition Service
Automatically tunes configuration based on system resources and workload
"""
import os
import sys
import json
import time
import asyncio
import aiohttp
import psutil
import argparse
from typing import Dict, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceOptimizer:
    """Automatic performance optimization for Face Recognition Service"""
    
    def __init__(self, service_url: str = "http://localhost:8000"):
        self.service_url = service_url
        self.system_info = self.get_system_info()
        self.current_config = {}
        self.optimization_results = {}
        
    def get_system_info(self) -> Dict[str, Any]:
        """Get system resource information"""
        return {
            "cpu_cores": psutil.cpu_count(),
            "memory_gb": psutil.virtual_memory().total / (1024**3),
            "cpu_freq_mhz": psutil.cpu_freq().current if psutil.cpu_freq() else 0,
            "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {}
        }
    
    async def get_service_metrics(self) -> Optional[Dict[str, Any]]:
        """Get current service performance metrics"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.service_url}/performance-metrics") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"Could not get metrics: HTTP {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Error getting service metrics: {e}")
            return None
    
    async def get_cache_stats(self) -> Optional[Dict[str, Any]]:
        """Get cache performance statistics"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.service_url}/cache-stats") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"Could not get cache stats: HTTP {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return None
    
    def calculate_optimal_workers(self) -> int:
        """Calculate optimal number of Gunicorn workers"""
        cpu_cores = self.system_info["cpu_cores"]
        memory_gb = self.system_info["memory_gb"]
        
        # For CPU-intensive face recognition: cores * 1.5 + 1
        cpu_based = int(cpu_cores * 1.5) + 1
        
        # Memory constraint: assume 500MB per worker
        memory_based = int(memory_gb * 1024 / 500)
        
        # Take the minimum to avoid resource exhaustion
        optimal = min(cpu_based, memory_based, 16)  # Cap at 16 workers
        
        logger.info(f"System: {cpu_cores} cores, {memory_gb:.1f}GB RAM")
        logger.info(f"CPU-based workers: {cpu_based}, Memory-based: {memory_based}")
        logger.info(f"Recommended workers: {optimal}")
        
        return optimal
    
    def calculate_optimal_batch_size(self) -> int:
        """Calculate optimal similarity batch size"""
        memory_gb = self.system_info["memory_gb"]
        cpu_cores = self.system_info["cpu_cores"]
        
        # Base batch size on available memory and CPU cores
        if memory_gb >= 8 and cpu_cores >= 8:
            return 100  # High-end server
        elif memory_gb >= 4 and cpu_cores >= 4:
            return 50   # Mid-range server
        else:
            return 25   # Low-end server
    
    def calculate_optimal_connections(self) -> Dict[str, int]:
        """Calculate optimal connection pool sizes"""
        workers = self.calculate_optimal_workers()
        
        # MongoDB connections: 20-30 per worker
        mongodb_max = workers * 25
        mongodb_min = max(workers * 5, 10)
        
        # Redis connections: 15-20 per worker
        redis_max = workers * 20
        
        return {
            "mongodb_max": min(mongodb_max, 300),  # Cap at 300
            "mongodb_min": mongodb_min,
            "redis_max": min(redis_max, 200)       # Cap at 200
        }
    
    def generate_optimized_config(self) -> Dict[str, Any]:
        """Generate optimized configuration"""
        workers = self.calculate_optimal_workers()
        batch_size = self.calculate_optimal_batch_size()
        connections = self.calculate_optimal_connections()
        
        config = {
            # Worker Configuration
            "GUNICORN_WORKERS": workers,
            
            # Performance Settings
            "SIMILARITY_BATCH_SIZE": batch_size,
            "MAX_CONCURRENT_SIMILARITY_CALCULATIONS": min(workers * 10, 150),
            
            # Connection Pools
            "MONGODB_MAX_POOL_SIZE": connections["mongodb_max"],
            "MONGODB_MIN_POOL_SIZE": connections["mongodb_min"],
            "REDIS_MAX_CONNECTIONS": connections["redis_max"],
            
            # Cache Settings (based on memory)
            "CACHE_TTL_SECONDS": 3600 if self.system_info["memory_gb"] >= 4 else 1800,
            "CACHE_ENABLED": "true",
            "CACHE_WARM_ON_STARTUP": "true",
            
            # Timeouts
            "REQUEST_TIMEOUT": 30,
            "UPLOAD_TIMEOUT": 60,
            "MONGODB_MAX_IDLE_TIME_MS": 30000,
        }
        
        return config
    
    async def benchmark_current_performance(self) -> Dict[str, Any]:
        """Benchmark current service performance"""
        logger.info("Benchmarking current performance...")
        
        # Get current metrics
        metrics = await self.get_service_metrics()
        cache_stats = await self.get_cache_stats()
        
        if not metrics:
            logger.warning("Could not get performance metrics")
            return {}
        
        benchmark = {
            "timestamp": time.time(),
            "system_info": self.system_info,
            "service_metrics": metrics,
            "cache_stats": cache_stats,
            "recommendations": self.generate_optimized_config()
        }
        
        return benchmark
    
    def save_optimization_report(self, report: Dict[str, Any], filename: str = "optimization_report.json"):
        """Save optimization report to file"""
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        logger.info(f"Optimization report saved to {filename}")
    
    def print_optimization_summary(self, config: Dict[str, Any]):
        """Print optimization summary"""
        print("\n" + "="*60)
        print("PERFORMANCE OPTIMIZATION RECOMMENDATIONS")
        print("="*60)
        
        print(f"\n🖥️  System Information:")
        print(f"   CPU Cores: {self.system_info['cpu_cores']}")
        print(f"   Memory: {self.system_info['memory_gb']:.1f} GB")
        
        print(f"\n⚙️  Recommended Configuration:")
        for key, value in config.items():
            print(f"   {key}={value}")
        
        print(f"\n📝 Environment Variables to Set:")
        for key, value in config.items():
            print(f"export {key}={value}")
        
        print(f"\n🐳 Docker Compose Override:")
        print("environment:")
        for key, value in config.items():
            print(f"  - {key}={value}")
        
        print("\n" + "="*60)
    
    async def run_optimization(self, save_report: bool = True) -> Dict[str, Any]:
        """Run complete optimization analysis"""
        logger.info("Starting performance optimization analysis...")
        
        # Generate optimized configuration
        optimized_config = self.generate_optimized_config()
        
        # Benchmark current performance
        benchmark = await self.benchmark_current_performance()
        benchmark["optimized_config"] = optimized_config
        
        # Print summary
        self.print_optimization_summary(optimized_config)
        
        # Save report
        if save_report:
            self.save_optimization_report(benchmark)
        
        return benchmark


async def main():
    """Main optimization function"""
    parser = argparse.ArgumentParser(description="Optimize Face Recognition Service Performance")
    parser.add_argument("--url", default="http://localhost:8000", help="Service URL")
    parser.add_argument("--no-save", action="store_true", help="Don't save optimization report")
    parser.add_argument("--output", help="Output file for optimization report")
    
    args = parser.parse_args()
    
    optimizer = PerformanceOptimizer(args.url)
    
    try:
        report = await optimizer.run_optimization(save_report=not args.no_save)
        
        if args.output:
            optimizer.save_optimization_report(report, args.output)
        
        print("\n✅ Optimization analysis complete!")
        print("💡 Apply the recommended environment variables and restart the service.")
        
        return 0
        
    except Exception as e:
        logger.error(f"Optimization failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
