import numpy as np
import logging
from typing import List, Tu<PERSON>, Optional
from models import FaceV<PERSON>, FaceEmbedding
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimilarityCalculator:
    
    def __init__(self, similarity_threshold: Optional[float] = None):
        if similarity_threshold is None:
            similarity_threshold = config.FACE_SIMILARITY_THRESHOLD
        self.similarity_threshold = similarity_threshold
    
    def cosine_similarity(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        try:
            v1 = np.array(vector1)
            v2 = np.array(vector2)
            
            dot_product = np.dot(v1, v2)
            
            magnitude1 = np.linalg.norm(v1)
            magnitude2 = np.linalg.norm(v2)
            
            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0
            
            similarity = dot_product / (magnitude1 * magnitude2)
            
            similarity = max(0.0, min(1.0, similarity))
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    def euclidean_distance(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """
        Calculate Euclidean distance between two vectors
        
        Args:
            vector1: First face encoding vector
            vector2: Second face encoding vector
            
        Returns:
            Euclidean distance (lower values indicate higher similarity)
        """
        try:
            # Ensure vectors are numpy arrays
            v1 = np.array(vector1)
            v2 = np.array(vector2)
            
            # Calculate Euclidean distance
            distance = np.linalg.norm(v1 - v2)
            
            return float(distance)
            
        except Exception as e:
            logger.error(f"Error calculating Euclidean distance: {e}")
            return float('inf')
    
    def face_distance(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """
        Calculate face distance using face_recognition's method
        This is essentially Euclidean distance but optimized for face recognition
        
        Args:
            vector1: First face encoding vector
            vector2: Second face encoding vector
            
        Returns:
            Face distance (lower values indicate higher similarity)
        """
        try:
            import face_recognition
            
            # Ensure vectors are numpy arrays with correct shape
            v1 = np.array(vector1).reshape(1, -1)
            v2 = np.array(vector2)
            
            # Use face_recognition's distance calculation
            distances = face_recognition.face_distance(v1, v2)
            
            return float(distances[0])
            
        except Exception as e:
            logger.error(f"Error calculating face distance: {e}")
            return float('inf')
    
    def find_best_match(self, target_vector: List[float],
                       candidate_embeddings: List[FaceEmbedding]) -> Optional[Tuple[FaceEmbedding, float, float]]:
        """
        Find the best matching face from a list of candidates

        Args:
            target_vector: The face vector to match against
            candidate_embeddings: List of face embeddings to search through

        Returns:
            Tuple of (best_match_embedding, similarity_score, confidence) or None if no match found
        """
        try:
            target_array = np.array(target_vector)
            best_match = None
            best_score = float('inf')  # Using distance, so lower is better

            for embedding in candidate_embeddings:
                for face_vector in embedding.faces:
                    # Calculate face distance (lower = more similar)
                    distance = self.face_distance(target_array, face_vector.vector)

                    if distance < best_score:
                        best_score = distance
                        best_match = embedding

            # Check if best match meets threshold
            if best_match and best_score <= self.similarity_threshold:
                # Calculate confidence score (0.0 to 1.0, higher is better)
                confidence = self.distance_to_confidence(best_score)
                logger.info(f"Found match with distance: {best_score}, confidence: {confidence}")
                return best_match, best_score, confidence

            logger.info(f"No match found. Best distance: {best_score}, Threshold: {self.similarity_threshold}")
            return None

        except Exception as e:
            logger.error(f"Error finding best match: {e}")
            return None

    def distance_to_confidence(self, distance: float) -> float:
        """
        Convert face distance to confidence score

        Args:
            distance: Face distance (lower = more similar)

        Returns:
            Confidence score (0.0 to 1.0, higher = more confident)
        """
        try:
            # Convert distance to confidence using exponential decay
            # Distance of 0 = confidence of 1.0
            # Distance of threshold = confidence of ~0.5
            # Distance > threshold = confidence < 0.5
            confidence = np.exp(-distance * 2.0)

            # Ensure confidence is between 0 and 1
            confidence = max(0.0, min(1.0, confidence))

            return float(confidence)

        except Exception as e:
            logger.error(f"Error converting distance to confidence: {e}")
            return 0.0
    
    def calculate_average_similarity(self, target_vector: List[float], 
                                   face_vectors: List[FaceVector]) -> float:
        """
        Calculate average similarity between target vector and a list of face vectors
        
        Args:
            target_vector: The target face vector
            face_vectors: List of face vectors to compare against
            
        Returns:
            Average similarity score
        """
        try:
            if not face_vectors:
                return 0.0
            
            target_array = np.array(target_vector)
            similarities = []
            
            for face_vector in face_vectors:
                similarity = self.cosine_similarity(target_array, face_vector.vector)
                similarities.append(similarity)
            
            average_similarity = np.mean(similarities)
            return float(average_similarity)
            
        except Exception as e:
            logger.error(f"Error calculating average similarity: {e}")
            return 0.0
    
    def is_match(self, distance: float) -> bool:
        """
        Determine if a distance score indicates a match
        
        Args:
            distance: Face distance score
            
        Returns:
            True if distance indicates a match, False otherwise
        """
        return distance <= self.similarity_threshold
    
    def set_threshold(self, threshold: float):
        """
        Update the similarity threshold
        
        Args:
            threshold: New threshold value
        """
        if 0.0 <= threshold <= 1.0:
            self.similarity_threshold = threshold
            logger.info(f"Updated similarity threshold to: {threshold}")
        else:
            logger.warning(f"Invalid threshold value: {threshold}. Must be between 0.0 and 1.0")


# Global similarity calculator instance
similarity_calculator = SimilarityCalculator()
