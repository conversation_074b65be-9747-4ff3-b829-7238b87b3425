#!/bin/bash

# Docker build script with memory optimization for Face Recognition Service

set -e

echo "🔨 Building Face Recognition Service Docker Image..."
echo "📊 Using memory-optimized build process..."

# Set Docker buildkit memory limit
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain

# Build with memory constraints and platform specification
docker buildx build \
    --platform=linux/amd64 \
    --memory=4g \
    --memory-swap=6g \
    --shm-size=1g \
    --progress=plain \
    -t rivopelu12/face-recognition-service:latest \
    -t rivopelu12/face-recognition-service:prod \
    .

echo "✅ Docker build completed successfully!"
echo "📦 Image tags created:"
echo "   - rivopelu12/face-recognition-service:latest"
echo "   - rivopelu12/face-recognition-service:prod"

# Optional: Push to Docker Hub
read -p "🚀 Push to Docker Hub? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📤 Pushing to Docker Hub..."
    docker push rivopelu12/face-recognition-service:latest
    docker push rivopelu12/face-recognition-service:prod
    echo "✅ Push completed!"
else
    echo "⏭️  Skipping push to Docker Hub"
fi

echo "🎉 Build process completed!"
