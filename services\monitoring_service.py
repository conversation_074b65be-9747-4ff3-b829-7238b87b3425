"""
Performance monitoring service for Face Recognition Service
Tracks metrics, response times, and system performance
"""
import time
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
import psutil
import threading
from config import config

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """Thread-safe performance metrics collector"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self._lock = threading.Lock()
        
        # Response time tracking
        self.response_times = deque(maxlen=max_samples)
        self.endpoint_metrics = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'errors': 0
        })
        
        # System metrics
        self.system_metrics = {
            'cpu_percent': deque(maxlen=100),
            'memory_percent': deque(maxlen=100),
            'memory_used_mb': deque(maxlen=100),
            'active_connections': deque(maxlen=100)
        }
        
        # Face recognition specific metrics
        self.face_metrics = {
            'validations_per_minute': deque(maxlen=60),
            'registrations_per_minute': deque(maxlen=60),
            'similarity_calculations': 0,
            'cache_operations': 0,
            'database_operations': 0
        }
        
        # Start time
        self.start_time = datetime.utcnow()
        
    def record_request(self, endpoint: str, response_time: float, status_code: int):
        """Record request metrics"""
        with self._lock:
            self.response_times.append(response_time)
            
            metrics = self.endpoint_metrics[endpoint]
            metrics['count'] += 1
            metrics['total_time'] += response_time
            metrics['min_time'] = min(metrics['min_time'], response_time)
            metrics['max_time'] = max(metrics['max_time'], response_time)
            
            if status_code >= 400:
                metrics['errors'] += 1
    
    def record_system_metrics(self):
        """Record current system metrics"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            with self._lock:
                self.system_metrics['cpu_percent'].append(cpu_percent)
                self.system_metrics['memory_percent'].append(memory.percent)
                self.system_metrics['memory_used_mb'].append(memory.used / 1024 / 1024)
                
        except Exception as e:
            logger.warning(f"Error recording system metrics: {e}")
    
    def increment_face_metric(self, metric_name: str, count: int = 1):
        """Increment face recognition specific metrics"""
        with self._lock:
            if metric_name in self.face_metrics:
                if isinstance(self.face_metrics[metric_name], int):
                    self.face_metrics[metric_name] += count
    
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        with self._lock:
            uptime = datetime.utcnow() - self.start_time
            
            # Calculate response time statistics
            if self.response_times:
                response_times_list = list(self.response_times)
                avg_response_time = sum(response_times_list) / len(response_times_list)
                min_response_time = min(response_times_list)
                max_response_time = max(response_times_list)
                
                # Calculate percentiles
                sorted_times = sorted(response_times_list)
                p50 = sorted_times[len(sorted_times) // 2]
                p95 = sorted_times[int(len(sorted_times) * 0.95)]
                p99 = sorted_times[int(len(sorted_times) * 0.99)]
            else:
                avg_response_time = min_response_time = max_response_time = 0
                p50 = p95 = p99 = 0
            
            # Calculate endpoint statistics
            endpoint_stats = {}
            for endpoint, metrics in self.endpoint_metrics.items():
                if metrics['count'] > 0:
                    endpoint_stats[endpoint] = {
                        'requests': metrics['count'],
                        'avg_response_time': metrics['total_time'] / metrics['count'],
                        'min_response_time': metrics['min_time'],
                        'max_response_time': metrics['max_time'],
                        'error_rate': (metrics['errors'] / metrics['count']) * 100,
                        'requests_per_second': metrics['count'] / uptime.total_seconds()
                    }
            
            # System metrics averages
            system_stats = {}
            for metric, values in self.system_metrics.items():
                if values:
                    system_stats[metric] = {
                        'current': values[-1],
                        'average': sum(values) / len(values),
                        'max': max(values)
                    }
            
            return {
                'uptime_seconds': uptime.total_seconds(),
                'uptime_formatted': str(uptime),
                'total_requests': sum(m['count'] for m in self.endpoint_metrics.values()),
                'response_times': {
                    'average': round(avg_response_time, 4),
                    'min': round(min_response_time, 4),
                    'max': round(max_response_time, 4),
                    'p50': round(p50, 4),
                    'p95': round(p95, 4),
                    'p99': round(p99, 4)
                },
                'endpoints': endpoint_stats,
                'system': system_stats,
                'face_recognition': dict(self.face_metrics)
            }


class MonitoringService:
    """Main monitoring service"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_running = False
        
    async def start_monitoring(self):
        """Start background monitoring"""
        if self.is_running:
            return
        
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop background monitoring"""
        self.is_running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_running:
            try:
                # Record system metrics every 10 seconds
                self.metrics.record_system_metrics()
                await asyncio.sleep(10)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(10)
    
    def record_request(self, endpoint: str, response_time: float, status_code: int):
        """Record request metrics"""
        self.metrics.record_request(endpoint, response_time, status_code)
    
    def increment_face_metric(self, metric_name: str, count: int = 1):
        """Increment face recognition metrics"""
        self.metrics.increment_face_metric(metric_name, count)
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        summary = self.metrics.get_summary()
        
        # Add cache statistics if available
        if config.CACHE_ENABLED:
            try:
                from services.cache_service import cache_service
                cache_stats = await cache_service.get_cache_stats()
                summary['cache'] = cache_stats
            except Exception as e:
                logger.warning(f"Could not get cache stats: {e}")
                summary['cache'] = {"error": str(e)}
        
        return summary
    
    async def get_health_metrics(self) -> Dict[str, Any]:
        """Get health check metrics"""
        try:
            # System health
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Service health
            uptime = datetime.utcnow() - self.metrics.start_time
            total_requests = sum(m['count'] for m in self.metrics.endpoint_metrics.values())
            
            # Calculate error rate
            total_errors = sum(m['errors'] for m in self.metrics.endpoint_metrics.values())
            error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
            
            health_status = "healthy"
            issues = []
            
            # Health checks
            if cpu_percent > 90:
                health_status = "degraded"
                issues.append(f"High CPU usage: {cpu_percent}%")
            
            if memory.percent > 90:
                health_status = "degraded"
                issues.append(f"High memory usage: {memory.percent}%")
            
            if error_rate > 10:
                health_status = "degraded"
                issues.append(f"High error rate: {error_rate:.1f}%")
            
            return {
                "status": health_status,
                "uptime_seconds": uptime.total_seconds(),
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent
                },
                "service": {
                    "total_requests": total_requests,
                    "error_rate": round(error_rate, 2),
                    "avg_response_time": self.metrics.get_summary()['response_times']['average']
                },
                "issues": issues
            }
            
        except Exception as e:
            logger.error(f"Error getting health metrics: {e}")
            return {
                "status": "unknown",
                "error": str(e)
            }


# Global monitoring service instance
monitoring_service = MonitoringService()
