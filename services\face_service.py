import face_recognition
import numpy as np
import logging
from typing import List, Optional, <PERSON><PERSON>
from PIL import Image
import io
from fastapi import UploadFile
from models import FaceVector
from config import config
from PIL import Image, UnidentifiedImageError
from PIL import Image, ImageOps
import numpy as np
import face_recognition
from typing import List, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FaceService:
     
    def __init__(self):
        self.supported_formats = {'jpg', 'jpeg', 'png', 'bmp', 'tiff'}
        self.max_image_size_mb = config.MAX_IMAGE_SIZE_MB
        self.min_registration_images = config.MIN_REGISTRATION_IMAGES
        
    @staticmethod
    def validate_image_format(filename: str) -> bool:
        valid_extensions = ['.jpg', '.jpeg', '.png']
        return any(filename.lower().endswith(ext) for ext in valid_extensions)


   
    async def load_image_from_upload(self, upload_file: UploadFile) -> Optional[np.ndarray]:
        try:
            content = await upload_file.read()

            # Perbaiki orientasi EXIF (dari kamera HP)
            image = Image.open(io.BytesIO(content))
            image = ImageOps.exif_transpose(image)

            if image.mode != 'RGB':
                image = image.convert('RGB')

            image_array = np.array(image)

            logger.info(f"Successfully loaded image: {upload_file.filename}, shape: {image_array.shape}")
            return image_array

        except Exception as e:
            logger.exception(f"Error loading image {upload_file.filename}: {e}")
            return None

    def detect_faces(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        try:
            logger.debug(f"Running face detection on image shape: {image.shape}")
            
            # Mulai dengan model ringan dulu (cepat)
            face_locations = face_recognition.face_locations(image, model='hog')

            
            logger.info(f"Detected {len(face_locations)} face(s) in image")
            return face_locations

        except Exception as e:
            logger.exception(f"Error detecting faces: {e}")
            return []
    
    def extract_face_encoding(self, image: np.ndarray, 
                            face_location: Optional[Tuple[int, int, int, int]] = None) -> Optional[np.ndarray]:
        try:
            if face_location:
                face_encodings = face_recognition.face_encodings(image, [face_location])
            else:
                face_encodings = face_recognition.face_encodings(image)
            
            if len(face_encodings) == 0:
                logger.warning("No face encodings found in image")
                return None
            
            if len(face_encodings) > 1:
                logger.warning(f"Multiple faces found ({len(face_encodings)}), using first one")
            
            encoding = face_encodings[0]
            logger.info(f"Successfully extracted face encoding with {len(encoding)} dimensions")
            return encoding
            
        except Exception as e:
            logger.error(f"Error extracting face encoding: {e}")
            return None
    
    async def process_single_image(self, upload_file: UploadFile) -> Optional[FaceVector]:
        try:
            image = await self.load_image_from_upload(upload_file)
            if image is None:
                return None
            
            face_locations = self.detect_faces(image)
            
            if len(face_locations) == 0:
                logger.warning(f"No faces detected in {upload_file.filename}")
                return None
            
            if len(face_locations) > 1:
                logger.warning(f"Multiple faces detected in {upload_file.filename}, skipping")
                return None
            
            face_encoding = self.extract_face_encoding(image, face_locations[0])
            if face_encoding is None:
                return None
            
            face_vector = FaceVector(vector=face_encoding.tolist())
            logger.info(f"Successfully processed {upload_file.filename}")
            return face_vector
            
        except Exception as e:
            logger.error(f"Error processing image {upload_file.filename}: {e}")
            return None
    
    async def process_multiple_images(self, upload_files: List[UploadFile]) -> List[FaceVector]:
        face_vectors = []
        
        for upload_file in upload_files:
            await upload_file.seek(0)
            
            face_vector = await self.process_single_image(upload_file)
            if face_vector:
                face_vectors.append(face_vector)
        
        logger.info(f"Successfully processed {len(face_vectors)} out of {len(upload_files)} images")
        return face_vectors
    
    def validate_minimum_images(self, face_vectors: List[FaceVector], minimum: Optional[int] = None) -> bool:
        if minimum is None:
            minimum = self.min_registration_images
        is_valid = len(face_vectors) >= minimum
        if not is_valid:
            logger.warning(f"Insufficient face vectors: {len(face_vectors)} < {minimum}")
        return is_valid
    
    def normalize_encoding(self, encoding: np.ndarray) -> np.ndarray:
        try:
            norm = np.linalg.norm(encoding)
            if norm == 0:
                return encoding
            return encoding / norm
            
        except Exception as e:
            logger.error(f"Error normalizing encoding: {e}")
            return encoding


face_service = FaceService()
