"""
Redis-based caching service for face recognition embeddings
Provides high-performance caching with TTL, invalidation, and warming strategies
"""
import json
import logging
import asyncio
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from models import FaceEmbedding
from config import config

# Configure logging
logger = logging.getLogger(__name__)


class CacheService:
    """High-performance Redis caching service for face embeddings"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool: Optional[ConnectionPool] = None
        self.is_connected = False
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_requests": 0,
            "last_refresh": None
        }
        
    async def connect(self) -> bool:
        """Initialize Redis connection with connection pooling"""
        try:
            # Create connection pool for high concurrency
            self.connection_pool = ConnectionPool(
                host=config.REDIS_HOST,
                port=config.REDIS_PORT,
                password=config.REDIS_PASSWORD if config.REDIS_PASSWORD else None,
                db=config.REDIS_DB,
                max_connections=config.REDIS_MAX_CONNECTIONS,
                socket_timeout=config.REDIS_SOCKET_TIMEOUT,
                socket_connect_timeout=config.REDIS_SOCKET_CONNECT_TIMEOUT,
                decode_responses=True,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis_client.ping()
            self.is_connected = True
            
            logger.info("Successfully connected to Redis cache")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Close Redis connection and cleanup"""
        try:
            if self.redis_client:
                await self.redis_client.close()
            if self.connection_pool:
                await self.connection_pool.disconnect()
            self.is_connected = False
            logger.info("Disconnected from Redis cache")
        except Exception as e:
            logger.error(f"Error disconnecting from Redis: {e}")
    
    async def is_cache_available(self) -> bool:
        """Check if cache is available and connected"""
        if not config.CACHE_ENABLED or not self.is_connected:
            return False
        
        try:
            await self.redis_client.ping()
            return True
        except Exception:
            self.is_connected = False
            return False
    
    async def cache_face_embeddings(self, embeddings: List[FaceEmbedding]) -> bool:
        """Cache face embeddings with TTL"""
        if not await self.is_cache_available():
            return False
        
        try:
            # Convert embeddings to JSON-serializable format
            embeddings_data = []
            for embedding in embeddings:
                embedding_dict = embedding.dict(by_alias=True)
                # Convert ObjectId to string if present
                if "_id" in embedding_dict:
                    embedding_dict["_id"] = str(embedding_dict["_id"])
                embeddings_data.append(embedding_dict)
            
            # Cache with TTL
            cache_data = {
                "embeddings": embeddings_data,
                "cached_at": datetime.utcnow().isoformat(),
                "count": len(embeddings_data)
            }
            
            await self.redis_client.setex(
                config.CACHE_EMBEDDINGS_KEY,
                config.CACHE_TTL_SECONDS,
                json.dumps(cache_data)
            )
            
            logger.info(f"Cached {len(embeddings_data)} face embeddings with TTL {config.CACHE_TTL_SECONDS}s")
            return True
            
        except Exception as e:
            logger.error(f"Error caching face embeddings: {e}")
            return False
    
    async def get_cached_face_embeddings(self) -> Optional[List[FaceEmbedding]]:
        """Retrieve cached face embeddings"""
        if not await self.is_cache_available():
            return None
        
        try:
            cached_data = await self.redis_client.get(config.CACHE_EMBEDDINGS_KEY)
            
            if not cached_data:
                self._cache_stats["misses"] += 1
                logger.debug("Cache miss for face embeddings")
                return None
            
            # Parse cached data
            cache_info = json.loads(cached_data)
            embeddings_data = cache_info["embeddings"]
            
            # Convert back to FaceEmbedding objects
            embeddings = []
            for embedding_dict in embeddings_data:
                try:
                    embeddings.append(FaceEmbedding(**embedding_dict))
                except Exception as e:
                    logger.warning(f"Error deserializing cached embedding: {e}")
                    continue
            
            self._cache_stats["hits"] += 1
            logger.debug(f"Cache hit: Retrieved {len(embeddings)} face embeddings")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error retrieving cached face embeddings: {e}")
            self._cache_stats["misses"] += 1
            return None
    
    async def invalidate_cache(self, pattern: str = None) -> bool:
        """Invalidate cache entries"""
        if not await self.is_cache_available():
            return False
        
        try:
            if pattern:
                # Delete keys matching pattern
                keys = await self.redis_client.keys(pattern)
                if keys:
                    await self.redis_client.delete(*keys)
                    logger.info(f"Invalidated {len(keys)} cache entries matching pattern: {pattern}")
            else:
                # Clear specific embeddings cache
                await self.redis_client.delete(config.CACHE_EMBEDDINGS_KEY)
                logger.info("Invalidated face embeddings cache")
            
            return True
            
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}")
            return False
    
    async def warm_cache(self, embeddings: List[FaceEmbedding]) -> bool:
        """Warm cache with face embeddings on startup"""
        logger.info("Starting cache warming process...")
        
        success = await self.cache_face_embeddings(embeddings)
        if success:
            self._cache_stats["last_refresh"] = datetime.utcnow().isoformat()
            logger.info(f"Cache warmed successfully with {len(embeddings)} embeddings")
        else:
            logger.warning("Cache warming failed")
        
        return success
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        self._cache_stats["total_requests"] = self._cache_stats["hits"] + self._cache_stats["misses"]
        
        if self._cache_stats["total_requests"] > 0:
            hit_rate = (self._cache_stats["hits"] / self._cache_stats["total_requests"]) * 100
        else:
            hit_rate = 0.0
        
        stats = {
            **self._cache_stats,
            "hit_rate_percentage": round(hit_rate, 2),
            "cache_enabled": config.CACHE_ENABLED,
            "cache_connected": await self.is_cache_available(),
            "ttl_seconds": config.CACHE_TTL_SECONDS
        }
        
        # Cache stats in Redis for monitoring
        if await self.is_cache_available():
            try:
                await self.redis_client.setex(
                    config.CACHE_STATS_KEY,
                    300,  # 5 minutes TTL for stats
                    json.dumps(stats)
                )
            except Exception as e:
                logger.warning(f"Could not cache stats: {e}")
        
        return stats
    
    async def refresh_cache_if_needed(self, embeddings: List[FaceEmbedding]) -> bool:
        """Refresh cache if it's stale or missing"""
        if not await self.is_cache_available():
            return False
        
        try:
            # Check if cache exists and is fresh
            cached_data = await self.redis_client.get(config.CACHE_EMBEDDINGS_KEY)
            
            if not cached_data:
                # Cache miss - refresh
                return await self.cache_face_embeddings(embeddings)
            
            # Check cache age
            cache_info = json.loads(cached_data)
            cached_at = datetime.fromisoformat(cache_info["cached_at"])
            age_seconds = (datetime.utcnow() - cached_at).total_seconds()
            
            if age_seconds > config.CACHE_REFRESH_INTERVAL:
                logger.info(f"Cache is {age_seconds}s old, refreshing...")
                return await self.cache_face_embeddings(embeddings)
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking cache freshness: {e}")
            return False


# Global cache service instance
cache_service = CacheService()
