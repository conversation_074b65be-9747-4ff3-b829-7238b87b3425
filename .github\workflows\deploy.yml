name: Deploy to VPS using Docker Hub

on:
  push:
    branches:
      - development

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Decode .env from base64
        run: |
          echo "📦 Decoding .env file from secret..."
          echo "${{ secrets.ENV_BASE64 }}" | base64 -d > .env

          # Validate
          if [ ! -s .env ]; then
            echo "❌ ERROR: Failed to decode .env!"
            exit 1
          fi
          echo "✅ .env file decoded successfully"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: rivopelu12/face-recognition-service:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          # Build will fail if .env file is missing or invalid
          build-args: |
            VALIDATE_ENV=true

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to VPS
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            # Login to Docker Hub
            echo "${{ secrets.DOCKER_HUB_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_HUB_USERNAME }}" --password-stdin

            echo "🧹 Hentikan dan hapus container lama"
            docker stop face-recognition-api || true
            docker rm -f face-recognition-api || true

            echo "📦 Pull image terbaru"
            docker pull rivopelu12/face-recognition-service:latest

            echo "🚀 Jalankan container baru dengan environment variables"
            docker run -d --name face-recognition-api \
              -p 8000:8000 \
              -e MONGODB_CONNECTION_STRING="${{ secrets.MONGODB_CONNECTION_STRING }}" \
              -e ENVIRONMENT=production \
              -e DEBUG=false \
              -e HOST=0.0.0.0 \
              -e PORT=8000 \
              -e MONGODB_DATABASE_NAME=face_recognition \
              -e MONGODB_COLLECTION_NAME=face_embeddings \
              -e FACE_SIMILARITY_THRESHOLD=0.6 \
              -e MIN_REGISTRATION_IMAGES=5 \
              -e MAX_IMAGE_SIZE_MB=10 \
              -e LOG_LEVEL=INFO \
              --restart unless-stopped \
              --memory="1g" \
              --cpus="0.5" \
              --health-cmd="curl -f http://localhost:8000/health || exit 1" \
              --health-interval=30s \
              --health-timeout=10s \
              --health-start-period=40s \
              --health-retries=3 \
              rivopelu12/face-recognition-service:latest

            echo "🧹 Bersihkan image dan container lama"
            docker image prune -f
            docker container prune -f

            echo "✅ Deployment selesai!"

            # Tunggu container siap dan cek status
            sleep 10
            docker ps | grep face-recognition-api
            echo "📋 Container logs:"
            docker logs --tail=20 face-recognition-api

            echo "🔍 Cek koneksi MongoDB..."
            docker exec face-recognition-api python -c "
            import os
            from pymongo import MongoClient
            try:
                client = MongoClient(os.getenv('MONGODB_CONNECTION_STRING'))
                client.admin.command('ping')
                print('✅ MongoDB connection successful!')
            except Exception as e:
                print(f'❌ MongoDB connection failed: {e}')
                        " || echo "MongoDB connection check failed"
