# Face Recognition Service - High Performance Deployment Guide

This guide covers deploying the enhanced Face Recognition Service optimized for 1000+ concurrent users.

## 🚀 Quick Start (Docker Compose - Recommended)

### 1. Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM available
- 2+ CPU cores recommended

### 2. Deploy with Docker Compose
```bash
# Clone and navigate to project directory
cd face-service-v2

# Start all services (Face Recognition + Redis + Nginx)
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f face-recognition-service
```

### 3. Verify Deployment
```bash
# Health check
curl http://localhost/health

# Cache statistics
curl http://localhost/cache-stats

# Performance metrics
curl http://localhost/performance-metrics
```

## 🔧 Production Deployment

### 1. Environment Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Redis (Ubuntu/Debian)
sudo apt update
sudo apt install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 2. Environment Variables
Create a `.env` file:
```bash
# Server Configuration
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# MongoDB Configuration
MONGODB_CONNECTION_STRING=********************************************?authSource=prod_testing
MONGODB_DATABASE_NAME=prod_testing
MONGODB_COLLECTION_NAME=face_embeddings
MONGODB_MAX_POOL_SIZE=200
MONGODB_MIN_POOL_SIZE=20

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=200

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600
CACHE_WARM_ON_STARTUP=true
CACHE_REFRESH_INTERVAL=1800

# Performance Configuration
MAX_CONCURRENT_SIMILARITY_CALCULATIONS=100
SIMILARITY_BATCH_SIZE=50
ENABLE_ASYNC_PROCESSING=true
GUNICORN_WORKERS=8

# Face Recognition Configuration
FACE_SIMILARITY_THRESHOLD=0.6
MIN_REGISTRATION_IMAGES=5
MAX_IMAGE_SIZE_MB=10
```

### 3. Start Production Server
```bash
# Using the production startup script
python start_production.py

# Or manually with Gunicorn
gunicorn main:app --config gunicorn.conf.py
```

## 📊 Performance Tuning

### 1. Worker Configuration
```bash
# Calculate optimal workers: (CPU cores * 1.5) + 1
# For 8-core server: (8 * 1.5) + 1 = 13 workers
export GUNICORN_WORKERS=13

# For high-memory servers, increase batch size
export SIMILARITY_BATCH_SIZE=100
export MAX_CONCURRENT_SIMILARITY_CALCULATIONS=150
```

### 2. Redis Optimization
```bash
# Redis configuration for high performance
redis-cli CONFIG SET maxmemory 1gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET save ""  # Disable persistence for speed
```

### 3. MongoDB Optimization
```bash
# Increase connection pool for high concurrency
export MONGODB_MAX_POOL_SIZE=300
export MONGODB_MIN_POOL_SIZE=50
```

## 🔍 Monitoring & Maintenance

### 1. Health Monitoring
```bash
# Basic health check
curl http://localhost:8000/health

# Detailed health with metrics
curl http://localhost:8000/health-detailed

# Performance metrics
curl http://localhost:8000/performance-metrics
```

### 2. Cache Management
```bash
# Check cache statistics
curl http://localhost:8000/cache-stats

# Manual cache refresh
curl -X POST http://localhost:8000/cache-refresh

# Clear cache (Redis CLI)
redis-cli FLUSHDB
```

### 3. Log Monitoring
```bash
# Monitor application logs
tail -f /var/log/face-recognition-service.log

# Monitor Nginx access logs
tail -f /var/log/nginx/access.log

# Monitor system resources
htop
```

## 🧪 Load Testing

### 1. Basic Load Test
```bash
# Test with 100 concurrent users
python tests/load_test.py --users 100 --duration 60

# Test with 500 concurrent users
python tests/load_test.py --users 500 --duration 120

# Test with 1000 concurrent users (full capacity)
python tests/load_test.py --users 1000 --duration 300 --output load_test_results.json
```

### 2. Interpreting Results
- **Success Rate**: Should be >99%
- **Average Response Time**: <500ms for cached requests
- **95th Percentile**: <1000ms
- **Requests/Second**: >100 per worker

### 3. Performance Benchmarks
Expected performance on a 4-core, 8GB RAM server:
- **Concurrent Users**: 1000+
- **Throughput**: 800+ requests/second
- **Cache Hit Rate**: >95%
- **Memory Usage**: <4GB
- **CPU Usage**: <80%

## 🚨 Troubleshooting

### 1. High Memory Usage
```bash
# Check memory usage
free -h
docker stats

# Reduce batch size
export SIMILARITY_BATCH_SIZE=25
export MAX_CONCURRENT_SIMILARITY_CALCULATIONS=50
```

### 2. Cache Issues
```bash
# Check Redis connection
redis-cli ping

# Check cache stats
curl http://localhost:8000/cache-stats

# Restart Redis
sudo systemctl restart redis-server
```

### 3. Database Connection Issues
```bash
# Check MongoDB connection
mongo --eval "db.adminCommand('ping')"

# Increase connection timeout
export MONGODB_MAX_IDLE_TIME_MS=60000
```

### 4. High Response Times
```bash
# Check if cache is working
curl http://localhost:8000/cache-stats

# Warm cache manually
curl -X POST http://localhost:8000/cache-refresh

# Check system resources
htop
iostat -x 1
```

## 🔒 Security Considerations

### 1. Network Security
- Use HTTPS in production
- Configure firewall rules
- Limit Redis access to localhost
- Use MongoDB authentication

### 2. Rate Limiting
Nginx configuration includes rate limiting:
- API endpoints: 10 requests/second
- Upload endpoints: 2 requests/second

### 3. Resource Limits
Docker containers have resource limits:
- Memory: 4GB limit
- CPU: 2 cores limit

## 📈 Scaling Beyond 1000 Users

### 1. Horizontal Scaling
```bash
# Scale Docker services
docker-compose up -d --scale face-recognition-service=4

# Use multiple Redis instances (sharding)
# Use MongoDB replica sets
```

### 2. Advanced Optimizations
- Implement vector databases (Pinecone, Weaviate)
- Use GPU acceleration for face recognition
- Implement CDN for static assets
- Use message queues for async processing

## 📞 Support

For issues or questions:
1. Check logs: `docker-compose logs face-recognition-service`
2. Verify configuration: `curl http://localhost:8000/health-detailed`
3. Run load tests: `python tests/load_test.py --users 10 --duration 30`

The service is now ready for production deployment with high-performance caching and monitoring!
