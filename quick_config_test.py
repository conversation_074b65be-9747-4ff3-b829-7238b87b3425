#!/usr/bin/env python3
"""
Quick test to verify the config import issue is resolved
"""

import requests
import io
from PIL import Image
import numpy as np

BASE_URL = "http://localhost:8000"

def create_small_test_image():
    """Create a very small test image"""
    # Create a tiny 50x50 RGB image
    img_array = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_config_import():
    """Test if the config import issue is resolved"""
    print("🔧 Testing Config Import Fix")
    print("=" * 40)
    
    try:
        # Create small test image
        test_image = create_small_test_image()
        
        # Prepare the request
        files = {
            'image': ('test.jpg', test_image, 'image/jpeg')
        }
        
        # Make the request with shorter timeout
        print("📤 Sending request...")
        response = requests.post(f"{BASE_URL}/validate-face", files=files, timeout=10)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 500:
            result = response.json()
            if "name 'config' is not defined" in str(result):
                print("❌ Config import issue still exists!")
                return False
            else:
                print("⚠️  Different 500 error (not config related)")
                print(f"📋 Error: {result}")
                return True
        else:
            print("✅ No config import error!")
            result = response.json()
            print(f"📋 Response: {result}")
            return True
            
    except requests.exceptions.Timeout:
        print("⏱️  Request timed out (but no config error)")
        return True
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_config_import()
    print("\n" + "=" * 40)
    if success:
        print("🎉 Config import issue is FIXED!")
    else:
        print("❌ Config import issue still exists")
