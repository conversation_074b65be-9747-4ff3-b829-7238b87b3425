# Multi-stage build for Python Face Recognition Service
FROM python:3.11-slim-bullseye AS builder

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    cmake \
    pkg-config \
    libopenblas-dev \
    liblapack-dev \
    libboost-python-dev \
    libboost-system-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libatlas-base-dev \
    gfortran \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install wheel
RUN pip install --upgrade pip setuptools wheel

# Install core web framework dependencies
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    gunicorn==21.2.0 \
    starlette==0.27.0 \
    pydantic==2.5.0 \
    python-multipart==0.0.6 \
    python-dotenv==1.0.0

# Install database and HTTP dependencies
RUN pip install --no-cache-dir \
    pymongo==4.6.0 \
    redis==6.4.0 \
    httpx==0.25.2 \
    httpcore==1.0.9 \
    dnspython==2.7.0 \
    psutil==7.0.0 \
    sentry-sdk==2.35.0

# Install image processing dependencies
RUN pip install --no-cache-dir \
    numpy==1.26.4 \
    pillow==10.4.0

# Install dlib with memory optimization
RUN pip install --no-cache-dir --no-build-isolation dlib==19.24.2

# Install face recognition
RUN pip install --no-cache-dir \
    face-recognition-models==0.3.0 \
    face-recognition==1.3.0

# Install additional requirements if exist
COPY requirements-docker.txt* ./
RUN if [ -f "requirements-docker.txt" ]; then \
    pip install --no-cache-dir -r requirements-docker.txt; \
    else \
    echo "No additional requirements file found"; \
    fi

# ================= RUNTIME STAGE ===================
FROM python:3.11-slim-bullseye AS runtime

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    DEBIAN_FRONTEND=noninteractive \
    ENVIRONMENT=production

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libopenblas0 \
    liblapack3 \
    libboost-python1.74.0 \
    libboost-system1.74.0 \
    libjpeg62-turbo \
    libpng16-16 \
    libtiff5 \
    libatlas3-base \
    libgfortran5 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

COPY --from=builder /opt/venv /opt/venv

RUN groupadd -r facerecog && useradd -r -g facerecog -d /app -s /bin/bash facerecog

WORKDIR /app

COPY main.py config.py models.py gunicorn.conf.py start_production.py ./
COPY services/ ./services/
COPY routes/ ./routes/
COPY utils/ ./utils/
COPY middleware/ ./middleware/

# Create necessary directories and set proper ownership
RUN mkdir -p /app/logs /app/uploads /app/temp && \
    chown -R facerecog:facerecog /app && \
    chmod 755 /app/logs /app/uploads /app/temp

USER facerecog

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["python", "start_production.py"]