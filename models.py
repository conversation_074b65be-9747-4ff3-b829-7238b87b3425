from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from bson import ObjectId


class PyObjectId(ObjectId):
    """Custom ObjectId class for Pydantic models"""
    @classmethod
    def __get_pydantic_core_schema__(cls, source_type, handler):
        from pydantic_core import core_schema
        return core_schema.no_info_plain_validator_function(cls.validate)

    @classmethod
    def validate(cls, v):
        if isinstance(v, ObjectId):
            return v
        if isinstance(v, str) and ObjectId.is_valid(v):
            return ObjectId(v)
        raise ValueError("Invalid ObjectId")

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, handler):
        field_schema.update(type="string")
        return field_schema


class FaceVector(BaseModel):
    """Model for individual face vector"""
    vector: List[float] = Field(..., description="128-dimensional face encoding vector")
    
    @validator('vector')
    def validate_vector_length(cls, v):
        if len(v) != 128:
            raise ValueError('Face vector must be exactly 128 dimensions')
        return v


class FaceEmbedding(BaseModel):
    """MongoDB document model for face embeddings"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    account_id: str = Field(..., description="Unique account identifier")
    name: str = Field(..., description="Person's name")
    work_unit_id: Optional[str] = Field(None, description="Work unit identifier")
    agency_id: Optional[str] = Field(None, description="Agency identifier")
    faces: List[FaceVector] = Field(..., description="List of face encoding vectors")
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class RegisterFaceRequest(BaseModel):
    """Request model for face registration"""
    account_id: str = Field(..., description="Unique account identifier")
    name: str = Field(..., description="Person's name")
    work_unit_id: Optional[str] = Field(None, description="Work unit identifier")
    agency_id: Optional[str] = Field(None, description="Agency identifier")


class RegisterFaceResponse(BaseModel):
    """Response model for face registration"""
    message: str
    account_id: str
    faces_processed: int
    total_faces_stored: int


class ValidateFaceRequest(BaseModel):
    """Request model for face validation - simplified (no required parameters)"""
    pass  # No required parameters - only image file is needed


class ValidateFaceResponse(BaseModel):
    """Response model for successful face validation"""
    account_id: str = Field(..., description="Unique account identifier of matched person")
    name: str = Field(..., description="Name of matched person")
    work_unit_id: Optional[str] = Field(None, description="Work unit identifier of matched person")
    agency_id: Optional[str] = Field(None, description="Agency identifier of matched person")
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0, higher is better)")
    similarity_score: float = Field(..., description="Similarity score (lower is better, ≤ threshold means match)")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    detail: Optional[str] = None


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    message: str
    database_connected: bool
