#!/usr/bin/env python3
"""
Production startup script for Face Recognition Service
Handles multi-worker deployment with proper resource management
"""
import os
import sys
import subprocess
import multiprocessing
from pathlib import Path

def get_optimal_workers():
    """Calculate optimal number of workers based on system resources"""
    cpu_count = multiprocessing.cpu_count()
    
    # For CPU-intensive face recognition tasks, use fewer workers than typical web apps
    # Formula: (CPU cores * 1.5) + 1, but cap at reasonable limit
    optimal_workers = min(int(cpu_count * 1.5) + 1, 16)
    
    # Override with environment variable if set
    workers = int(os.getenv('GUNICORN_WORKERS', optimal_workers))
    
    print(f"System has {cpu_count} CPU cores")
    print(f"Using {workers} Gunicorn workers")
    
    return workers

def check_dependencies():
    """Check if all required services are available"""
    print("Checking dependencies...")
    
    # Check Redis connection if caching is enabled
    cache_enabled = os.getenv('CACHE_ENABLED', 'true').lower() == 'true'
    if cache_enabled:
        try:
            import redis
            redis_host = os.getenv('REDIS_HOST', 'localhost')
            redis_port = int(os.getenv('REDIS_PORT', '6379'))
            
            r = redis.Redis(host=redis_host, port=redis_port, socket_timeout=5)
            r.ping()
            print(f"✓ Redis connection successful ({redis_host}:{redis_port})")
        except Exception as e:
            print(f"⚠ Redis connection failed: {e}")
            print("  Cache will be disabled")
            os.environ['CACHE_ENABLED'] = 'false'
    
    # Check MongoDB connection
    try:
        from pymongo import MongoClient
        from config import config
        
        client = MongoClient(config.MONGODB_CONNECTION_STRING, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        client.close()
        print("✓ MongoDB connection successful")
    except Exception as e:
        print(f"✗ MongoDB connection failed: {e}")
        sys.exit(1)
    
    print("All dependency checks passed!")

def setup_environment():
    """Setup production environment variables"""
    # Set production defaults
    os.environ.setdefault('ENVIRONMENT', 'production')
    os.environ.setdefault('DEBUG', 'false')
    os.environ.setdefault('RELOAD', 'false')
    os.environ.setdefault('LOG_LEVEL', 'INFO')
    
    # Performance settings
    os.environ.setdefault('MONGODB_MAX_POOL_SIZE', '200')
    os.environ.setdefault('MONGODB_MIN_POOL_SIZE', '20')
    os.environ.setdefault('REDIS_MAX_CONNECTIONS', '200')
    os.environ.setdefault('MAX_CONCURRENT_SIMILARITY_CALCULATIONS', '100')
    os.environ.setdefault('SIMILARITY_BATCH_SIZE', '50')
    
    # Cache settings
    os.environ.setdefault('CACHE_ENABLED', 'true')
    os.environ.setdefault('CACHE_TTL_SECONDS', '3600')
    os.environ.setdefault('CACHE_WARM_ON_STARTUP', 'true')
    
    print("Environment configured for production")

def start_server():
    """Start the Gunicorn server with optimal configuration"""
    workers = get_optimal_workers()
    
    # Gunicorn command
    cmd = [
        'gunicorn',
        'main:app',
        '--config', 'gunicorn.conf.py',
        '--workers', str(workers),
        '--worker-class', 'uvicorn.workers.UvicornWorker',
        '--bind', f"{os.getenv('HOST', '0.0.0.0')}:{os.getenv('PORT', '8000')}",
        '--preload',
        '--access-logfile', '-',
        '--error-logfile', '-',
        '--log-level', os.getenv('LOG_LEVEL', 'info').lower()
    ]
    
    print(f"Starting Face Recognition Service with command:")
    print(' '.join(cmd))
    print()
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
    except subprocess.CalledProcessError as e:
        print(f"Server failed to start: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("=" * 60)
    print("FACE RECOGNITION SERVICE - PRODUCTION STARTUP")
    print("=" * 60)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Setup environment
    setup_environment()
    
    # Check dependencies
    check_dependencies()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
